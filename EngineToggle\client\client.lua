-- CLIENTSIDED

-- Registers a network event
RegisterNetEvent('EngineToggle:Engine')
RegisterNetEvent('EngineToggle:RPDamage')

local vehicles = {}; RPWorking = true

Citizen.CreateThread(function()
	while true do
		local sleep = 500
		if <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> then
			sleep = 10
			if IsControlJustReleased(1, <PERSON><PERSON><PERSON><PERSON>) then
				sleep = 10
				TriggerEvent('EngineToggle:Engine')
			end
		end
		if GetSeatPedIsTryingToEnter(PlayerPedId()) == -1 and not table.contains(vehicles, GetVehiclePedIsTryingToEnter(PlayerPedId())) then
			sleep = 10
			table.insert(vehicles, {GetVehiclePedIsTryingToEnter(PlayerPedId()), IsVehicleEngineOn(GetVehiclePedIsTryingToEnter(PlayerPedId()))})
		elseif IsPedInAnyVehicle(PlayerPedId(), false) and not table.contains(vehicles, GetVehiclePedIsIn(PlayerPedId(), false)) then
			table.insert(vehicles, {GetVehiclePedIsIn(PlayerPedId(), false), IsVehicleEngineOn(GetVehiclePedIsIn(PlayerPedId(), false))})
		end
		for i, vehicle in ipairs(vehicles) do
			sleep = 10
			if DoesEntityExist(vehicle[1]) then
				sleep = 10
				if (GetPedInVehicleSeat(vehicle[1], -1) == PlayerPedId()) or IsVehicleSeatFree(vehicle[1], -1) then
					sleep = 10
					if RPWorking then
						sleep = 10
						SetVehicleEngineOn(vehicle[1], vehicle[2], true, false)
						SetVehicleJetEngineOn(vehicle[1], vehicle[2])
						if not IsPedInAnyVehicle(PlayerPedId(), false) or (IsPedInAnyVehicle(PlayerPedId(), false) and vehicle[1]~= GetVehiclePedIsIn(PlayerPedId(), false)) then
							sleep = 10
							if IsThisModelAHeli(GetEntityModel(vehicle[1])) or IsThisModelAPlane(GetEntityModel(vehicle[1])) then
								sleep = 10
								if vehicle[2] then
									sleep = 10
									SetHeliBladesFullSpeed(vehicle[1])
								end
							end
						end
					end
				end
			else
				table.remove(vehicles, i)
			end
		end
		Citizen.Wait(0)
	end
end)

AddEventHandler('EngineToggle:Engine', function()
	local veh
	local StateIndex
	for i, vehicle in ipairs(vehicles) do
		if vehicle[1] == GetVehiclePedIsIn(PlayerPedId(), false) then
			veh = vehicle[1]
			StateIndex = i
		end
	end
	Citizen.Wait(1500)
	if IsPedInAnyVehicle(PlayerPedId(), false) then 
		if (GetPedInVehicleSeat(veh, -1) == PlayerPedId()) then
			vehicles[StateIndex][2] = not GetIsVehicleEngineRunning(veh)
			if vehicles[StateIndex][2] then
				TriggerEvent('chatMessage', '', {0, 255, 0}, 'تم تشغيل المحرك')
			else
				TriggerEvent('chatMessage', '', {255, 0, 0}, 'تم إطفاء المحرك')
			end
		end 
    end 
end)

AddEventHandler('EngineToggle:RPDamage', function(State)
	RPWorking = State
end)

if OnAtEnter then
	Citizen.CreateThread(function()
		while true do
			local sleep = 500
			if GetSeatPedIsTryingToEnter(PlayerPedId()) == -1 then
				sleep = 10
				for i, vehicle in ipairs(vehicles) do
					sleep = 10
					if vehicle[1] == GetVehiclePedIsTryingToEnter(PlayerPedId()) and not vehicle[2] then
						sleep = 10
						Citizen.Wait(3500)
						vehicle[2] = true
						TriggerEvent('chatMessage', '', {0, 255, 0}, 'تم تشغيل المحرك')
					end
				end
			end
			Citizen.Wait(sleep)
		end
	end)
end

function table.contains(table, element)
  for _, value in pairs(table) do
    if value[1] == element then
      return true
    end
  end
  return false
end
