# حل مشاكل إعادة تشغيل السكريبت

## المشاكل المحلولة
1. **خطأ Streaming**: `GTA5_b3258.exe!sub_14133163C (0x37)` مرتبط بـ:
   - `CfxCollection_RemoveStreamingTag`
   - مشاكل في تسجيل الخطوط (Fonts) المخصصة
   - تضارب في ملفات streaming عند إعادة التشغيل

2. **خطأ Lua**: `attempt to call a nil value (global 'ParkVehicles')` في:
   - `esx_advancedgarage/server/server.lua:11`
   - مشكلة ترتيب تعريف الدوال

## الأسباب
1. **مشكلة Streaming**: في ملف `esx_legacyfuel/source/fuel_client.lua` كان يتم تسجيل خط مخصص `A9eelsh` بدون معالجة صحيحة لإعادة التشغيل
2. **مشكلة ترتيب الدوال**: في `esx_advancedgarage/server/server.lua` كانت دالة `ParkVehicles()` تُستدعى قبل تعريفها

## الحل المطبق

### 1. إنشاء نظام إدارة streaming آمن
- تم إنشاء ملف `streaming_fix.lua` لإدارة ملفات streaming بشكل آمن
- يتعامل مع تسجيل الخطوط المخصصة بطريقة تمنع التضارب
- يوفر تنظيف آمن عند إيقاف السكريبت

### 2. تحديث fxmanifest.lua
- إضافة `stream/A9eelsh.gfx` إلى قسم files
- إضافة `streaming_fix.lua` إلى client_scripts
- إضافة export للدالة `GetCustomFont`

### 3. تحديث esx_legacyfuel
- استبدال التسجيل المباشر للخط بنظام آمن
- استخدام export من esx_misc3 للحصول على الخط

### 4. إضافة معالجات الأحداث
- معالجة `onResourceStart` و `onResourceStop`
- تنظيف الموارد عند إيقاف السكريبت
- إعادة تهيئة آمنة عند بدء السكريبت

## الملفات المعدلة
1. `streaming_fix.lua` (جديد) - نظام إدارة streaming آمن
2. `restart_test.lua` (جديد) - سكريبت اختبار إعادة التشغيل
3. `fxmanifest.lua` - إضافة ملفات جديدة وexports
4. `esx_legacyfuel/source/fuel_client.lua` - إصلاح تسجيل الخطوط
5. `esx_advancedgarage/server/server.lua` - إصلاح ترتيب الدوال
6. `client_main.lua` - معالجات أحداث إعادة التشغيل
7. `server_main.lua` - معالجات أحداث السيرفر

## كيفية التحقق من الحل
1. قم بإعادة تشغيل السكريبت عدة مرات: `restart esx_misc3`
2. تأكد من عدم ظهور خطأ `GTA5_b3258.exe!sub_14133163C`
3. تأكد من عدم ظهور خطأ `attempt to call a nil value (global 'ParkVehicles')`
4. تحقق من عمل الخطوط المخصصة في واجهة الوقود
5. راقب console للتأكد من رسائل التهيئة الصحيحة
6. استخدم الأمر `/testmiscrestart` لاختبار إعادة التشغيل تلقائياً

## ملاحظات مهمة
- لا تقم بتعديل ملفات streaming مباشرة بعد الآن
- استخدم النظام الآمن المطبق لأي خطوط أو ملفات streaming جديدة
- تأكد من إضافة أي ملفات streaming جديدة إلى قسم files في fxmanifest.lua

## في حالة استمرار المشكلة
1. تحقق من أن جميع الملفات محدثة
2. تأكد من أن ملف `A9eelsh.gfx` موجود في مجلد stream
3. راجع console للتأكد من رسائل التهيئة
4. قم بإعادة تشغيل الخادم بالكامل إذا لزم الأمر
