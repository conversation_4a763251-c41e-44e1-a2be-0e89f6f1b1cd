# حل مشكلة خطأ Streaming عند إعادة تشغيل السكريبت

## المشكلة
كان يحدث خطأ `GTA5_b3258.exe!sub_14133163C (0x37)` عند إعادة تشغيل السكريبت، والذي كان مرتبط بـ:
- `CfxCollection_RemoveStreamingTag`
- مشاكل في تسجيل الخطوط (Fonts) المخصصة
- تضارب في ملفات streaming عند إعادة التشغيل

## السبب
المشكلة كانت في ملف `esx_legacyfuel/source/fuel_client.lua` حيث كان يتم تسجيل خط مخصص `A9eelsh` بدون معالجة صحيحة لإعادة التشغيل، مما يسبب تضارب في streaming tags.

## الحل المطبق

### 1. إنشاء نظام إدارة streaming آمن
- تم إنشاء ملف `streaming_fix.lua` لإدارة ملفات streaming بشكل آمن
- يتعامل مع تسجيل الخطوط المخصصة بطريقة تمنع التضارب
- يوفر تنظيف آمن عند إيقاف السكريبت

### 2. تحديث fxmanifest.lua
- إضافة `stream/A9eelsh.gfx` إلى قسم files
- إضافة `streaming_fix.lua` إلى client_scripts
- إضافة export للدالة `GetCustomFont`

### 3. تحديث esx_legacyfuel
- استبدال التسجيل المباشر للخط بنظام آمن
- استخدام export من esx_misc3 للحصول على الخط

### 4. إضافة معالجات الأحداث
- معالجة `onResourceStart` و `onResourceStop`
- تنظيف الموارد عند إيقاف السكريبت
- إعادة تهيئة آمنة عند بدء السكريبت

## الملفات المعدلة
1. `streaming_fix.lua` (جديد)
2. `fxmanifest.lua`
3. `esx_legacyfuel/source/fuel_client.lua`
4. `client_main.lua`
5. `server_main.lua`

## كيفية التحقق من الحل
1. قم بإعادة تشغيل السكريبت عدة مرات
2. تأكد من عدم ظهور خطأ `GTA5_b3258.exe!sub_14133163C`
3. تحقق من عمل الخطوط المخصصة في واجهة الوقود
4. راقب console للتأكد من رسائل التهيئة الصحيحة

## ملاحظات مهمة
- لا تقم بتعديل ملفات streaming مباشرة بعد الآن
- استخدم النظام الآمن المطبق لأي خطوط أو ملفات streaming جديدة
- تأكد من إضافة أي ملفات streaming جديدة إلى قسم files في fxmanifest.lua

## في حالة استمرار المشكلة
1. تحقق من أن جميع الملفات محدثة
2. تأكد من أن ملف `A9eelsh.gfx` موجود في مجلد stream
3. راجع console للتأكد من رسائل التهيئة
4. قم بإعادة تشغيل الخادم بالكامل إذا لزم الأمر
