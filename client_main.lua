ConfigReady = false
Config = {}
Config.Locale = 'en'

RegisterNetEvent('esx_misc3:updateconfig')
AddEventHandler('esx_misc3:updateconfig', function(opjeecctt)
    Config = opjeecctt
    ConfigReady = true
end)

AddEventHandler('playerSpawned', function()
	Citizen.Wait(5000)
	TriggerServerEvent('esx_misc3:spawned')
end)

Citizen.CreateThread(function()
  while true do
      Citizen.Wait(0)
      local veh = GetVehiclePedIsIn(PlayerPedId(), false)
      if DoesEntityExist(veh) and not IsEntityDead(veh) then
          local model = GetEntityModel(veh)
          -- If it's not a boat, plane or helicopter, and the vehilce is off the ground with ALL wheels, then block steering/leaning left/right/up/down.
          if not IsThisModel<PERSON>oat(model) and not IsThisModelAHeli(model) and not IsThisModelAPlane(model) and IsEntityInAir(veh) then
              DisableControlAction(0, 59) -- leaning left/right
              DisableControlAction(0, 60) -- leaning up/down
          end
      end
  end
end)

-----------------
govjob = false
leojob = false
leojob2 = false
adminjob = false
ESXDATA = {}

RegisterCommand('idc', function()
  print('id card')
  local handle = RegisterPedheadshotTransparent(PlayerPedId())
    while not IsPedheadshotReady(handle) or not IsPedheadshotValid(handle) do
        Wait(0)
    end
    local txd = GetPedheadshotTxdString(handle)

    -- Add the notification text, the more text you add the smaller the font
    -- size will become (text is forced on 1 line only), so keep this short!
    SetNotificationTextEntry("STRING")
    AddTextComponentSubstringPlayerName("Transparent Headshot")
    print(txd)
    -- Draw the notification
    DrawNotificationAward(txd, txd, 200, 0, "FM_GEN_UNLOCK")
    
    -- Cleanup after yourself!
    UnregisterPedheadshot(handle)
end)


Citizen.CreateThread(function()

  while ESXDATA.job == nil do
		Citizen.Wait(10)
	end
  
  ESXDATA = ESX.GetPlayerData()
  Citizen.Wait(500)
  isLeooradminorgovJob()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	ESXDATA = xPlayer
  Citizen.Wait(500)
  isLeooradminorgovJob()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESXDATA.job = job
  Citizen.Wait(500)
  isLeooradminorgovJob()
end)

function isLeooradminorgovJob()
  while ESXDATA.job == nil do
		Citizen.Wait(10)
	end

  if ESXDATA.job.name == 'admin' then
    adminjob = true
  else
    adminjob = false
  end

  if ESXDATA.job.name == 'police' or ESXDATA.job.name == 'ambulance' or ESXDATA.job.name == 'agent' or ESXDATA.job.name == 'admin' then
    govjob = true
  else
    govjob = false
  end

  if ESXDATA.job.name == 'police' or ESXDATA.job.name == 'agent' or ESXDATA.job.name == 'admin' then
    leojob = true
  else
    leojob = false
  end

  if ESXDATA.job.name == 'police' or ESXDATA.job.name == 'agent' then
    leojob2 = true
  else
    leojob2 = false
  end
  
end

function checkRequiredXPlevel(level)
  local plyLevel = exports.zahya_xplevel.ESXP_GetRank()

  if plyLevel >= level then
    return true
  else
    return false
  end
end

Citizen.CreateThread(function()
  ReplaceHudColour(134, 134)
end)

-- Handle resource restart to prevent streaming issues
AddEventHandler('onResourceStop', function(resource)
  if resource == GetCurrentResourceName() then
    -- Clean up any streaming assets or fonts that might cause conflicts
    print('[esx_misc3] Resource stopped, cleaning up streaming assets...')
  end
end)

AddEventHandler('onResourceStart', function(resource)
  if resource == GetCurrentResourceName() then
    -- Ensure proper initialization after restart
    print('[esx_misc3] Resource started, initializing streaming assets...')
    Citizen.Wait(1000) -- Give time for streaming to initialize
  end
end)