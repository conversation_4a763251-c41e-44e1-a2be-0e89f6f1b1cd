ESX.RegisterServerCallback('esx_advancedvehicleshop:buyVehicleC', function(source, cb, model, plate, category, name, typee, typeindata, joobbb, levelold, trunkkg, higherprice, lowerprice)
	local xPlayer = ESX.GetPlayerFromId(source)
	local modelPrice

	for k,v in ipairs(Config.VehiclesShop[typee].Vehicles) do
		if model == v.model then
			modelPrice = v.price
			break
		end
	end

	if modelPrice and xPlayer.getMoney() >= modelPrice then
		xPlayer.removeMoney(modelPrice)

		MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, type, job, priceold, category, levelold, trunkkg, higherprice, lowerprice, name) VALUES (@owner, @plate, @vehicle, @type, @job, @priceold, @category, @levelold, @trunkkg, @higherprice, @lowerprice, @name)', {
			['@owner'] = xPlayer.identifier,
			['@plate'] = plate,
			['@vehicle'] = json.encode({model = GetHashKey(model), plate = plate, engineHealth = 1000.0, bodyHealth = 1000.0}),
			['@type'] = typeindata,
			['@job'] = joobbb,
			['@priceold'] = modelPrice,
			['@category'] = category,
			['@levelold'] = levelold,
			['@trunkkg'] = trunkkg,
			['@higherprice'] = higherprice,
			['@lowerprice'] = lowerprice,
			['@name'] = name,
		}, function(rowsChanged)
			xPlayer.showNotification(_U('car_belongs', plate))
			TriggerEvent("zahya-logs:server:SendLog", "buyvehicle", "**شراء مركبة**", "red", "`: اسم المركبة` \n ".. model .."\n \n `: الوحة` \n ".. plate .."\n \n `: اسم المواطن` \n ".. xPlayer.name .."\n \n `: المبلغ` \n ".. modelPrice .."\n \n `: الحالة` \n جديد \n")
			cb(true)
		end)
	else
		cb(false)
	end
end)

-- Shared
--[[
ESX.RegisterServerCallback('esx_advancedvehicleshop:tebexVehicles', function(source, cb)

	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM tebex_vehicles WHERE identifier = @identifier', {

		['@identifier'] = xPlayer.identifier

	}, function(result)

		cb(result)

	end)

end)]]


ESX.RegisterServerCallback('esx_advancedvehicleshop:isPlateTaken', function(source, cb, plate)
	MySQL.Async.fetchAll('SELECT 1 FROM owned_vehicles WHERE plate = @plate', {
		['@plate'] = plate
	}, function(result)
		cb(result[1] ~= nil)
	end)
end)

ESX.RegisterServerCallback('esx_advancedvehicleshop:retrieveJobVehicles', function(source, cb, type)
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND type = @type AND job = @job', {
		['@owner'] = xPlayer.identifier,
		['@type'] = type,
		['@job'] = xPlayer.job.name
	}, function(result)
		cb(result)
	end)
end)

RegisterNetEvent('esx_advancedvehicleshop:setJobVehicleState')
AddEventHandler('esx_advancedvehicleshop:setJobVehicleState', function(plate, state, token)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)

	MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = @stored WHERE plate = @plate AND job = @job', {
		['@stored'] = state,
		['@plate'] = plate,
		['@job'] = xPlayer.job.name
	}, function(rowsChanged)
		if rowsChanged == 0 then
			print(('[esx_advancedvehicleshop] [^3WARNING^7] %s exploited the garage!'):format(xPlayer.identifier))
		end
	end)
end)
