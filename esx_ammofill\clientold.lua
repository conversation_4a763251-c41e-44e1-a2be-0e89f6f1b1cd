RegisterNetEvent('esx_ammofill:fillAmmo')
AddEventHandler('esx_ammofill:fillAmmo', function()
-- print("esx_ammofill:fillAmmo ^1[BY abdulrhman#1912]^0")
  ped = PlayerPedId()
  if IsPedArmed(ped, 4) then
    weapon=GetSelectedPedWeapon(ped)
    if weapon~=nil then
      ammo=GetAmmoInPedWeapon(ped,weapon)
      if ( weapon == GetHashKey("WEAPON_PISTOL") ) and ammo < 60 then
      TriggerServerEvent('esx_ammofill:remove', 'WEAPON_PISTOL', 60)
      ESX.ShowNotification('استخدام تعبئة طلقات')
      ESX.ShowNotification('يمكن تعبأة 60 طلقة فقط بالسلاح')
    elseif ( weapon == GetHashKey("weapon_pumpshotgun") ) and ammo < 30 then
      TriggerServerEvent('esx_ammofill:remove', 'weapon_pumpshotgun', 30)
      ESX.ShowNotification('استخدام تعبئة طلقات')
      ESX.ShowNotification('يمكن تعبأة 30 طلقة فقط بالسلاح')
    return
    elseif (weapon == GetHashKey("weapon_microsmg") ) and ammo < 70 then
      TriggerServerEvent('esx_ammofill:remove', 'weapon_microsmg', 70)
      ESX.ShowNotification('استخدام تعبئة طلقات')
      ESX.ShowNotification('يمكن تعبأة 70 طلقة فقط بالسلاح')
    return
    elseif (weapon == GetHashKey("weapon_carbinerifle") ) and ammo < 80 then
      TriggerServerEvent('esx_ammofill:remove', 'weapon_carbinerifle', 80)
      ESX.ShowNotification('استخدام تعبئة طلقات')
      ESX.ShowNotification('يمكن تعبأة 80 طلقة فقط بالسلاح')
    return
    elseif (weapon == GetHashKey("weapon_doubleaction") ) and ammo < 50 then
      TriggerServerEvent('esx_ammofill:remove', 'weapon_doubleaction', 50)
      ESX.ShowNotification('استخدام تعبئة طلقات')
      ESX.ShowNotification('يمكن تعبأة 50 طلقة فقط بالسلاح')
    return
    end
    else
      ESX.ShowNotification('لايوجد سلاح بيدك للتعبأة')
    end
  else
    ESX.ShowNotification('<font color=red>لايمكن استخدام التعبئة لهذا السلاح')
  end
end)