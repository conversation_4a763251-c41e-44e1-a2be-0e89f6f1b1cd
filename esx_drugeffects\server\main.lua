
ESX.RegisterUsableItem('weed', function(source)
	local xPlayer = ESX.GetPlayerFromId(source)

	xPlayer.removeInventoryItem('weed', 1)
	TriggerClientEvent('esx_drugeffects:onWeed', source)
	Wait(5000)
	TriggerClientEvent('esx_status:add', source, 'drug', 92000)
end)

ESX.RegisterUsableItem('opium', function(source)
	local xPlayer = ESX.GetPlayerFromId(source)

	xPlayer.removeInventoryItem('opium', 1)
	TriggerClientEvent('esx_drugeffects:onOpium', source)
	Wait(5000)
	TriggerClientEvent('esx_status:add', source, 'drug', 89000)
end)

ESX.RegisterUsableItem('meth', function(source)
	local xPlayer = ESX.GetPlayerFromId(source)

	xPlayer.removeInventoryItem('meth', 1)
	TriggerClientEvent('esx_drugeffects:onMeth', source)
	Wait(5000)
	TriggerClientEvent('esx_status:add', source, 'drug', 93000)
end)

ESX.RegisterUsableItem('coke', function(source)
	local xPlayer = ESX.GetPlayerFromId(source)

	xPlayer.removeInventoryItem('coke', 1)
	TriggerClientEvent('esx_drugeffects:onCoke', source)
	Wait(5000)
	TriggerClientEvent('esx_status:add', source, 'drug', 89000)
end)

ESX.RegisterUsableItem('xanax', function(source)
	local xPlayer = ESX.GetPlayerFromId(source)

	xPlayer.removeInventoryItem('xanax', 1)

	TriggerClientEvent('esx_status:remove', source, 'drug', 44000)
end)
