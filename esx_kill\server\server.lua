
function sanitize(string)
    return string:gsub('%@', '')
end

exports('sanitize', function(string)
    sanitize(string)
end)

RegisterNetEvent("discordLogs")
AddEventHandler("discordLogs", function(message, color, channel)
    discordLog(message, color, channel)
end)

-- Get exports from server side
exports('discord', function(message, id, id2, color, channel)


	local ids = ExtractIdentifiers(id)
	local postal = getPlayerLocation(id)
	if Config_esx_kill.postal then _postal = "\n**Nearest Postal:** ".. postal .."" else _postal = "" end
	if Config_esx_kill.discordID then if ids.discord ~= "" then _discordID ="\n**Discord ID:** <@" ..ids.discord:gsub("discord:", "")..">" else _discordID = "\n**Discord ID:** N/A" end else _discordID = "" end
	if Config_esx_kill.steamID then if ids.steam ~= "" then _steamID ="\n**Steam ID:** " ..ids.steam.."" else _steamID = "\n**Steam ID:** N/A" end else _steamID = "" end
	if Config_esx_kill.steamURL then  if ids.steam ~= "" then _steamURL ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids.steam:gsub("steam:", ""),16).."" else _steamURL = "\n**Steam URL:** N/A" end else _steamURL = "" end
	if Config_esx_kill.playerID then _playerID ="\n**Player ID:** " ..id.."" else _playerID = "" end
	local player1 = message..'\n'.._playerID..''.. _postal ..''.. _discordID..''.._steamID..''.._steamURL
	_message = player1

	if id2 ~= 0 then
	local ids2 = ExtractIdentifiers(id2)
	local postal2 = getPlayerLocation(id2)
	if Config_esx_kill.postal then _postal2 = "\n**Nearest Postal:** ".. postal2 .."" else _postal2 = "" end
	if Config_esx_kill.discordID then if ids2.discord ~= "" then _discordID2 ="\n**Discord ID:** <@" ..ids2.discord:gsub("discord:", "")..">" else _discordID2 = "\n**Discord ID:** N/A" end else _discordID2 = "" end
	if Config_esx_kill.steamID then if ids2.steam ~= "" then _steamID2 ="\n**Steam ID:** " ..ids2.steam.."" else _steamID2 = "\n**Steam ID:** N/A" end else _steamID2 = "" end
	if Config_esx_kill.steamURL then  if ids2.steam ~= "" then _steamURL2 ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids2.steam:gsub("steam:", ""),16).."" else _steamURL2 = "\n**Steam URL:** N/A" end else _steamURL2 = "" end
	if Config_esx_kill.playerID then _playerID2 ="\n**Player ID:** " ..id2.."" else _playerID2 = "" end
	local player2 = _playerID2..''.. _postal2 ..''.. _discordID2..''.._steamID2..''.._steamURL2
	_message = player1..'\n'..player2
	end

    discordLog(_message, color, channel)
end)

-- Sending message to the All Logs channel and to the channel it has listed
function discordLog(message, color, channel)
  if Config_esx_kill.AllLogs then
	PerformHttpRequest(Config_esx_kill.webhooks["all"], function(err, text, headers) end, 'POST', json.encode({username = Config_esx_kill.username, embeds = {{["color"] = color, ["author"] = {["name"] = Config_esx_kill.communtiyName,["icon_url"] = Config_esx_kill.communtiyLogo}, ["description"] = "".. message .."",["footer"] = {["text"] = " "..os.date("%x %X %p"),["icon_url"] = "",},}}, avatar_url = Config_esx_kill.avatar}), { ['Content-Type'] = 'application/json' })
  end
	PerformHttpRequest(Config_esx_kill.webhooks[channel], function(err, text, headers) end, 'POST', json.encode({username = Config_esx_kill.username, embeds = {{["color"] = color, ["author"] = {["name"] = Config_esx_kill.communtiyName,["icon_url"] = Config_esx_kill.communtiyLogo}, ["description"] = "".. message .."",["footer"] = {["text"] = " "..os.date("%x %X %p"),["icon_url"] = "",},}}, avatar_url = Config_esx_kill.avatar}), { ['Content-Type'] = 'application/json' })
end

-- Event Handlers
-- Send message when Player died (including reason/killer check) (Not always working)
RegisterServerEvent('playerDied')
AddEventHandler('playerDied',function(id,player,killer,DeathReason,Weapon)
	local ids = ExtractIdentifiers(source)
	local postal = getPlayerLocation(source)
	local Code = math.random(1, 9)..RandomLetters(1)..math.random(111, 999)
	if DeathReason then _DeathReason = "`"..DeathReason.."`" else _DeathReason = "`died`" end
	if Weapon then _Weapon = ""..Weapon.."" else _Weapon = "" end
	if Config_esx_kill.postal then _postal = "\n**Nearest Postal:** ".. postal .."" else _postal = "" end
	if Config_esx_kill.discordID then if ids.discord ~= "" then _discordID ="\n**Discord ID:** <@" ..ids.discord:gsub("discord:", "")..">" else _discordID = "\n**Discord ID:** N/A" end else _discordID = "" end
	if Config_esx_kill.steamID then if ids.steam ~= "" then _steamID ="\n**Steam ID:** " ..ids.steam.."" else _steamID = "\n**Steam ID:** N/A" end else _steamID = "" end
	if Config_esx_kill.steamURL then  if ids.steam ~= "" then _steamURL ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids.steam:gsub("steam:", ""),16).."" else _steamURL = "\n**Steam URL:** N/A" end else _steamID = "" end
	if Config_esx_kill.playerID then _playerID ="\n**Player ID:** " ..source.."" else _playerID = "" end

	TriggerClientEvent("esx:showNotification", source, "<font size=5><p align=center><font color=red>"..Code.."</font> ".."</br> صور مقطع مع الرقم وارسل بالتكت في حال تقديم شكوى تخريب ضد لاعب<b>")

	if id == 1 then  -- Suicide/died
        discordLog('**' .. sanitize(GetPlayerName(source)) .. '** '.._DeathReason..''.._Weapon..''.._playerID..''.. _postal ..''.. _discordID..''.._steamID..''.._steamURL..'\n كود القتل '..Code..' ', Config_esx_kill.deathColor, 'deaths') -- sending to deaths channel
	elseif id == 2 then -- Killed by other player
	local ids2 = ExtractIdentifiers(killer)
	local postal2 = getPlayerLocation(killer)
	if Config_esx_kill.postal then _postal2 = "\n**Nearest Postal:** ".. postal2 .."" else _postal2 = "" end
	if Config_esx_kill.discordID then if ids2.discord ~= "" then _KillDiscordID ="\n**Discord ID:** <@" ..ids2.discord:gsub("discord:", "")..">" else _KillDiscordID = "\n**Discord ID:** N/A" end else _KillDiscordID = "" end
	if Config_esx_kill.steamID then if ids2.steam ~= "" then _KillSteamID ="\n**Steam ID:** " ..ids2.steam.."" else _KillSteamID = "\n**Steam ID:** N/A" end else _KillSteamID = "" end
	if Config_esx_kill.steamURL then  if ids2.steam ~= "" then _KillSteamURL ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids2.steam:gsub("steam:", ""),16).."" else _KillSteamURL = "\n**Steam URL:** N/A" end else _steamID = "" end
	if Config_esx_kill.playerID then _killPlayerID ="\n**Player ID:** " ..killer.."" else _killPlayerID = "" end
		discordLog('**' .. GetPlayerName(killer) .. '** '.._DeathReason..' ' .. GetPlayerName(source).. ' `('.._Weapon..')`\n\n**[Player INFO]**'.._playerID..''.. _postal ..''.. _discordID..''.._steamID..''.._steamURL..'\n كود القتل '..Code..'\n\n**[Killer INFO]**'.._killPlayerID..''.. _postal2 ..''.. _KillDiscordID..''.._KillSteamID..''.._KillSteamURL..'', Config_esx_kill.deathColor, 'deaths') -- sending to deaths channel
	else -- When gets killed by something else
        discordLog('**' .. sanitize(GetPlayerName(source)) .. '** `died`'.._playerID..''.. _postal ..''.. _discordID..''.._steamID..''.._steamURL..'\n كود القتل '..Code..' ', Config_esx_kill.deathColor, 'deaths') -- sending to deaths channel
		TriggerClientEvent("esx:showNotification", source, "<font size=5><p align=center><font color=red>"..Code.."</font> ".."</br> صور مقطع مع الرقم وارسل بالتكت في حال تقديم شكوى تخريب ضد لاعب<b>")
	end
	local xPlayer = ESX.GetPlayerFromId(killer)
    local xTarget = ESX.GetPlayerFromId(source)
	local xPlayerJob = xTarget.job.name
	-- local policecount = 150
	-- local agentcount = 150
	-- local emscount = 1500
	-- local meccount = 1500
	-- local kill = 0
	-- if xPlayerJob == 'police' then
	-- TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', killer, 'remove', policecount, 'قتل شرطي')
	-- kill = kill + 1
	-- xPlayer.showNotification('محاولة قتل موظف في إدارة الشرطة يخصم من خبرتك'..policecount..'عدد القتل'..kill..' خلال 15 دقيقة')
	-- return
	-- elseif xPlayerJob == 'agent' then
	-- TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', killer, 'remove', agentcount, 'قتل حرس')
	-- kill = kill + 1
	-- xPlayer.showNotification('محاولة قتل موظف في حرس الحدود يخصم من خبرتك'..agentcount..'عدد القتل'..kill..' خلال 15 دقيقة')
	-- return
	-- elseif xPlayerJob == 'ambulance' then
	-- TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', killer, 'remove', emscount, 'قتل مسعف')
	-- kill = kill + 1
	-- xPlayer.showNotification('محاولة قتل موظف في الهلال الاحمر يخصم من خبرتك'..emscount..'عدد القتل'..kill..' خلال 15 دقيقة')
	-- return
	-- elseif xPlayerJob == 'mechanic' then
	-- TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', killer, 'remove', meccount, 'قتل ميكانيكي')
	-- kill = kill + 1
    -- xPlayer.showNotification('محاولة قتل موظف في كراج الميكانيكي يخصم من خبرتك'..meccount..'عدد القتل'..kill..' خلال 15 دقيقة')
	-- return
	-- elseif xPlayerJob == 'admin' then
	-- TriggerEvent("esx_visa:KickAndRemoveVisaPlayer", killer, 'مخالفة النظام العام')
	-- return
	-- end
end) 



-- Send message when Player fires a weapon
 RegisterServerEvent('playerShotWeapon')
AddEventHandler('playerShotWeapon', function(weapon)
	local ids = ExtractIdentifiers(source)
	local postal = getPlayerLocation(source)
	if Config_esx_kill.postal then _postal = "\n**Nearest Postal:** ".. postal .."" else _postal = "" end
	if Config_esx_kill.discordID then if ids.discord ~= "" then _discordID ="\n**Discord ID:** <@" ..ids.discord:gsub("discord:", "")..">" else _discordID = "\n**Discord ID:** N/A" end else _discordID = "" end
	if Config_esx_kill.steamID then if ids.steam ~= "" then _steamID ="\n**Steam ID:** " ..ids.steam.."" else _steamID = "\n**Steam ID:** N/A" end else _steamID = "" end
	if Config_esx_kill.steamURL then  if ids.steam ~= "" then _steamURL ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids.steam:gsub("steam:", ""),16).."" else _steamURL = "\n**Steam URL:** N/A" end else _steamID = "" end
	if Config_esx_kill.playerID then _playerID ="\n**Player ID:** " ..source.."" else _playerID = "" end
	if Config_esx_kill.weaponLog then
		discordLog('**' .. sanitize(GetPlayerName(source))  .. '** fired a `' .. weapon .. '`'.._playerID..''.. _postal ..''.. _discordID..''.._steamID..''.._steamURL..'', Config_esx_kill.shootingColor, 'shooting')
    end
end) 

-- Getting exports from clientside
RegisterServerEvent('ClientDiscord')
AddEventHandler('ClientDiscord', function(message, id, id2, color, channel)

	local ids = ExtractIdentifiers(id)
	local postal = getPlayerLocation(id)
	if Config_esx_kill.postal then _postal = "\n**Nearest Postal:** ".. postal .."" else _postal = "" end
	if Config_esx_kill.discordID then if ids.discord ~= "" then _discordID ="\n**Discord ID:** <@" ..ids.discord:gsub("discord:", "")..">" else _discordID = "\n**Discord ID:** N/A" end else _discordID = "" end
	if Config_esx_kill.steamID then if ids.steam ~= "" then _steamID ="\n**Steam ID:** " ..ids.steam.."" else _steamID = "\n**Steam ID:** N/A" end else _steamID = "" end
	if Config_esx_kill.steamURL then  if ids.steam ~= "" then _steamURL ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids.steam:gsub("steam:", ""),16).."" else _steamURL = "\n**Steam URL:** N/A" end else _steamURL = "" end
	if Config_esx_kill.playerID then _playerID ="\n**Player ID:** " ..id.."" else _playerID = "" end
	local player1 = message..'\n'.._playerID..''.. _postal ..''.. _discordID..''.._steamID..''.._steamURL
	_message = player1

	if id2 ~= 0 then
	local ids2 = ExtractIdentifiers(id2)
	local postal2 = getPlayerLocation(id2)
	if Config_esx_kill.postal then _postal2 = "\n**Nearest Postal:** ".. postal2 .."" else _postal2 = "" end
	if Config_esx_kill.discordID then if ids2.discord ~= "" then _discordID2 ="\n**Discord ID:** <@" ..ids2.discord:gsub("discord:", "")..">" else _discordID2 = "\n**Discord ID:** N/A" end else _discordID2 = "" end
	if Config_esx_kill.steamID then if ids2.steam ~= "" then _steamID2 ="\n**Steam ID:** " ..ids2.steam.."" else _steamID2 = "\n**Steam ID:** N/A" end else _steamID2 = "" end
	if Config_esx_kill.steamURL then  if ids2.steam ~= "" then _steamURL2 ="\nhttps://steamcommunity.com/profiles/" ..tonumber(ids2.steam:gsub("steam:", ""),16).."" else _steamURL2 = "\n**Steam URL:** N/A" end else _steamURL2 = "" end
	if Config_esx_kill.playerID then _playerID2 ="\n**Player ID:** " ..id2.."" else _playerID2 = "" end
	local player2 = _playerID2..''.. _postal2 ..''.. _discordID2..''.._steamID2..''.._steamURL2
	_message = player1..'\n'..player2
	end

   discordLog(_message, color,  channel)
end)

function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
        end
    end

    return identifiers
end

function getPlayerLocation(src)

local raw = LoadResourceFile(GetCurrentResourceName(), GetResourceMetadata(GetCurrentResourceName(), 'postal_file'))
local postals = json.decode(raw)
local nearest = nil

local player = src
local ped = GetPlayerPed(player)
local playerCoords = GetEntityCoords(ped)

local x, y = table.unpack(playerCoords)

	local ndm = -1
	local ni = -1
	for i, p in ipairs(postals) do
		local dm = (x - p.x) ^ 2 + (y - p.y) ^ 2
		if ndm == -1 or dm < ndm then
			ni = i
			ndm = dm
		end
	end

	if ni ~= -1 then
		local nd = math.sqrt(ndm)
		nearest = {i = ni, d = nd}
	end
	_nearest = postals[nearest.i].code
	return _nearest
end

function RandomLetters(length)
    local res = ""
    for i = 1, length do
        res = res .. string.char(math.random(97, 122))
    end
    return res
end