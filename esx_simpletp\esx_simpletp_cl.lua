
Citizen.CreateThread(function()																							
	while true do
		local sleep = 1500
		player = PlayerPedId()
		coords = GetEntityCoords(player)
		
		for k, v in pairs(Config_esx_simpletp.Teleport) do
			if GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < v.View then 
				ESX.Game.Utils.DrawText3D(vector3(v.Pos.x, v.Pos.y, v.Pos.z), v.Text, 1.2, 4)
				DrawMarker(1, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.5, 4.5, 4.0, 255, 102, 0, 100, false, true, 2, false, nil, nil, false)
				sleep = 0
				if GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < v.Activate then
					sleep = 0
					if IsControlJustPressed(0, 38) then
						sleep = 0
						if Config_esx_simpletp.OnlyCars then
							sleep = 0
							if IsPedInAnyVehicle(player, false) then
								sleep = 0
								if Config_esx_simpletp.Blackout then
									sleep = 0
									DoScreenFadeOut(1000)
									Citizen.Wait(1500)
								end
								SetEntityHeading(player, v.Heading)
								ESX.Game.Teleport(GetVehiclePedIsUsing(player), v.Posout)
								if Config_esx_simpletp.Blackout then
									sleep = 0
									DoScreenFadeIn(1500)
								end
							end
						else
							if not IsPedInAnyVehicle(player, false) then
								sleep = 0
								if Config_esx_simpletp.Animation then
									sleep = 0
									RequestAnimDict("timetable@jimmy@doorknock@")
									while not HasAnimDictLoaded("timetable@jimmy@doorknock@") do
									Citizen.Wait(1000)
									end
										
									Citizen.Wait(200)
									TaskPlayAnim(player,"timetable@jimmy@doorknock@","knockdoor_idle",1.0, 1.0, 3000, 9, 1.0, 0, 0, 0)
									Citizen.Wait(3000)
								end
								if Config_esx_simpletp.Blackout then
									sleep = 0
									DoScreenFadeOut(1000)
									Citizen.Wait(1500)
								end
								SetEntityHeading(player, v.Heading)
								ESX.Game.Teleport(player, v.Posout) 
								if Config_esx_simpletp.Blackout then
									sleep = 0
									DoScreenFadeIn(1500)
								end
							end
						end
					end
				end
			end
		end
		Citizen.Wait(sleep)
    	end
end)
