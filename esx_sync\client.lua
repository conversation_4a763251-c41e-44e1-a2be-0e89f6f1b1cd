
RegisterFontFile('A9eelsh')
fontId = RegisterFontId('A9eelsh')

Citizen.CreateThread(function()

	Citizen.Wait(5000)
	PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	while ESX == nil do Citizen.Wait(10) end
	PlayerData = xPlayer
    ESX.TriggerServerCallback('sync:getvar', function(Status, time)
		Config_esx_sync.sync = Status
		Config_esx_sync.time = time 
        if Status then 
            Config_esx_sync.blip = AddBlipForRadius(Config_esx_sync.coords.x, Config_esx_sync.coords.y, Config_esx_sync.coords.z, Config_esx_sync.coords.size)
            SetBlipSprite(Config_esx_sync.blip, 9)
            SetBlipColour(Config_esx_sync.blip, 75)
            SetBlipScale(Config_esx_sync.blip, Config_esx_sync.coords.size)
            SetBlipAlpha(Config_esx_sync.blip, 100)
            SetBlipAsShortRange(Config_esx_sync.blip, false)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString("ss")
            EndTextCommandSetBlipName(Config_esx_sync.blip)
        else 
            RemoveBlip(Config_esx_sync.blip)
        end
	end)
end)

notification = {
}

RegisterNetEvent("zahya:delallveh")
AddEventHandler("zahya:delallveh", function ()
    for vehicle in EnumerateVehicles() do
        if (not IsPedAPlayer(GetPedInVehicleSeat(vehicle, -1))) then 
            SetVehicleHasBeenOwnedByPlayer(vehicle, false) 
            SetEntityAsMissionEntity(vehicle, false, false) 
            DeleteVehicle(vehicle)
            if (DoesEntityExist(vehicle)) then 
                DeleteVehicle(vehicle) 
            end
        end
    end
end)

RegisterNetEvent("zahya:sync:client:start")
AddEventHandler("zahya:sync:client:start", function(time)
	TriggerServerEvent("zahya:sync:server:start", time)
end)

RegisterNetEvent("zahya:sync:client:Wait")
AddEventHandler("zahya:sync:client:Wait", function()
	exports.pNotify:SendNotification({
        text = "</br><font size=4><p align=center><b><font color=orange> الرجاء الانتظار قبل بدء وقت مزامنة جديد</font>",
        type = "alert",
        queue = "left",
        timeout = 7000,
        killer = true,
        theme = "gta",
        layout = "centerLeft"
    })
end)

RegisterNetEvent("zahya:sync:client:serverstart")
AddEventHandler("zahya:sync:client:serverstart", function()
	Config_esx_sync.sync = true 
	RemoveBlip(Config_esx_sync.blip)
	Config_esx_sync.blip = AddBlipForRadius(Config_esx_sync.coords.x, Config_esx_sync.coords.y, Config_esx_sync.coords.z, Config_esx_sync.coords.size)
	SetBlipSprite(Config_esx_sync.blip, 9)
	SetBlipColour(Config_esx_sync.blip, 83)
	SetBlipScale(Config_esx_sync.blip, Config_esx_sync.coords.size)
	SetBlipAlpha(Config_esx_sync.blip, 100)
	SetBlipAsShortRange(Config_esx_sync.blip, false)
	BeginTextCommandSetBlipName('STRING')
	AddTextComponentString("ss")
	EndTextCommandSetBlipName(Config_esx_sync.blip)
	--InitialSetup()
	for k, v in pairs(notification) do 
		exports.pNotify:SendNotification({
			text = v,
			type = "alert",
			queue = "left",
			timeout = 7000,
			killer = true,
			theme = "gta",
			layout = "centerLeft"
		})
		Wait(3000)
	end
end)

RegisterNetEvent("zahya:sync:client:serverstop")
AddEventHandler("zahya:sync:client:serverstop", function()
	Config_esx_sync.sync = false 
	RemoveBlip(Config_esx_sync.blip)
	--InitialSetup()
	TriggerEvent("JoinTransistion:start")
	TriggerEvent("zahya:delallveh")
	exports.pNotify:SendNotification({
       text = '<font color=red>عليك الانتظار</font><font color=orange> '..bulletproof_cooltime..'</font> دقيقة</br>تم انتهاء من وقت المزامنة',
        type = "alert",
        queue = "left",
        timeout = 7000,
        killer = true,
        theme = "gta",
        layout = "centerLeft"
    })
end)

function GetStatussync()
    return Config_esx_sync.sync;
end
RegisterNetEvent("zahya:sync:client:updatetime")
AddEventHandler("zahya:sync:client:updatetime", function(time)
	Config_esx_sync.time = time 
end)
--AddEventHandler('playerSpawned', function()
--	TriggerEvent("JoinTransistion:start")
--end)