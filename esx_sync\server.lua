local istopped = false 

function syncStart (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "الرقابة والتفتيش"}
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function syncStartPrivate (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "الرقابة والتفتيش"}
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

RegisterCommand('sync', function(source, args, user)
	local time = tonumber(args[1])
    local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.job.name == 'admin' then
	if time ~= nil then 
        TriggerClientEvent("zahya:sync:client:start", source, time)
		--TriggerEvent("zahya:Drugs:itssynctime", 1)
		--TriggerEvent("zahya:Shops:itssynctime", 1)
		--TriggerEvent("zahya:Atm:itssynctime", 1)
	--	TriggerEvent("zahya:Bank:itssynctime", 1)
		--TriggerEvent("zahya:BankTwo:itssynctime", 1)
        syncStart((' وقت المزامنة ')," **اعلان وقت المزامنة | المدة "..time.."دقيقة** ", "*تنويه عام* \n\n*قم بتخزين مركباتك بالكراج بأسرع وقت",********)
        syncStartPrivate((' وقت المزامنة ')," اعلان وقت المزامنة | المدة "..time.."دقيقة ", "*من قبل الرقابي* \n\nSteam: `".. GetPlayerName(source).."`\nInGame name: `"..xPlayer.getName().."`",********)
	end
	else
	TriggerClientEvent('esx:showNotification', source, ' ﻂﻘﻓ ﺔﺑﺎﻗﺮﻠﻟ ﺡﺎﺘﻣ ﺮﻣﻷﺍ')
	end
end, false, {help = ("set time for the sync time")})

RegisterCommand('syncstop', function(source, args, user)
	local time = tonumber(args[1])
    local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.job.name == 'admin' then
    TriggerEvent("zahya:sync:server:stop")
	--TriggerEvent("zahya:Drugs:itssynctime", 0)
	--TriggerEvent("zahya:Shops:itssynctime", 0)
	--TriggerEvent("zahya:Atm:itssynctime", 0)
	--TriggerEvent("zahya:Bank:itssynctime", 0)
--	TriggerEvent("zahya:BankTwo:itssynctime", 0)
    syncStart((' وقت المزامنة '),"**تم ايقاف وقت المزامنة**", "*تنويه عام*\n عودة الحياة طبيعية في المنطقة",********)
    syncStartPrivate((' وقت المزامنة '),"**اعلان ايقاف وقت المزامنة**", "*من قبل الرقابي**\n\nSteam: `".. GetPlayerName(source).."`\nInGame name: `"..xPlayer.getName().."`",********)
	else
	TriggerClientEvent('esx:showNotification', source, ' الامر متاح للرقابة والتفتيش')
	end
end, false, {help = ("set time for the sync time")})

ESX.RegisterServerCallback('sync:getvar', function(source, cb)
	cb(Config_esx_sync.sync, Config_esx_sync.time)
end)

RegisterServerEvent("zahya:sync:server:start")
AddEventHandler("zahya:sync:server:start", function(time)
    local src = source
    if isstopped then 
        TriggerClientEvent("zahya:sync:client:Wait", src)
    else 
        if not Config_esx_sync.sync then 
            Config_esx_sync.sync = true
            TriggerClientEvent("zahya:sync:client:serverstart", -1)
            TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش | ", {198, 40, 40}, " تم الاعلان عن وقت المزامنة بعد "..time.." دقيقة ")
            TriggerEvent("InteractSound_SV:PlayOnAll", "synctime", 0.6)
            Config_esx_sync.time = time
            timing(Config_esx_sync.time)
            if Config_esx_sync.sync then 
                Config_esx_sync.sync = false
                TriggerClientEvent("zahya:sync:client:serverstop", -1)
                TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش | ", {198, 40, 40}, "تم الانتهاء من وقت المزامنة")
                syncStart((' وقت المزامنة '),"**تم ايقاف وقت المزامنة**", "*تنويه عام*\n عودة الحياة طبيعية في المنطقة",********)
                TriggerEvent("InteractSound_SV:PlayOnAll", "synctime", 0.6)
            end
        end
    end
end)

function timing(time)
    TriggerClientEvent("zahya:sync:client:updatetime", -1, time)
    Wait(60000)
    if isstopped then 
        isstopped = false 
        return
    end
    if Config_esx_sync.time > 0 then 
        Config_esx_sync.time = time - 1
        timing(Config_esx_sync.time) 
    end
end

RegisterServerEvent("zahya:sync:server:stop")
AddEventHandler("zahya:sync:server:stop", function()
    if Config_esx_sync.sync and not isstopped then 
        isstopped = true 
        Config_esx_sync.time = 0 
        TriggerClientEvent("zahya:sync:client:updatetime", -1, Config_esx_sync.time)
        Config_esx_sync.sync = false
        TriggerClientEvent("zahya:sync:client:serverstop", -1)
        TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش | ", {198, 40, 40}, "تم الانتهاء من وقت المزامنة")
        syncStart((' وقت المزامنة '),"**تم ايقاف وقت المزامنة**", "*تنويه عام*\n عودة الحياة طبيعية في المنطقة",********)
        TriggerEvent("InteractSound_SV:PlayOnAll", "synctime", 0.6)
    end
end)