$(document).ready(function(){
  // LUA listener
  window.addEventListener('message', function( event ) {
    if (event.data.action == 'open') {
      var type        = event.data.type;
      var mugshot        = event.data.mugshot;
      if (event.data.array['user'] != null) {
        var userData    = event.data.array['user'][0];
        // var sex         = userData.sex;
        var number         = userData.phone_number;
        }
      var licenseData = event.data.array['licenses'];
      var job         = event.data.array['job'];
      var jobname		= event.data.array['jobname'];
      var jobname2		= event.data.array['jobname2'];
      var gradelabel		= event.data.array['gradelabel'];
    $('#cardnumber').text('');
    $('#cardnumber').css('margin-top', '86px');
    $('#dob').text('');
    $('#signature').text('');
    $('#name').css('margin-top', '-2px');
    $('img').css('margin-left', '23px')
      if ( type == 'driver' || type == null || type == 'job') {
        $('img').show();
        // $('#name').css('color', '#282828');
        $('#cardnumber').text(number);
        $('img').attr('src', mugshot);
        if ( jobname == 'admin' ) {
          $('#name').text('غير مسجل');
        } else {
          $('#name').text(userData.firstname + ' ' + userData.lastname);
        }
        
        $('#name').text(userData.firstname + ' ' + userData.lastname);
        $('#dob').text(userData.dateofbirth);
        $('#height').text(userData.height);
        $('#signature').text(job/* userData.firstname + ' ' + userData.lastname */);
        $('#dob').css('margin-right', '90px');
        $('#dob').css('margin-top', '1px');
        if ( type == 'driver' ) {
          if ( licenseData != null ) {
          Object.keys(licenseData).forEach(function(key) {
            var type = licenseData[key].type;

            if ( type == 'drive_bike') {
              type = 'bike';
            } else if ( type == 'drive_truck' ) {
              type = 'truck';
            } else if ( type == 'drive' ) {
              type = 'car';
            }
            $('#dob').css('margin-right', '86px');
            $('#dob').css('margin-top', '-1px');
            // if ( type == 'bike' || type == 'truck' || type == 'car' ) {
            //   // $('#licenses').append('<p>'+ type +'</p>');
            // }
          });
        }

          $('#id-card').css('background', 'url(assets/images/license.png)');
          $('#cardnumber').css('margin-top', '83px');
          $('#height').text('');
          $('#signature').text('');
          $('#cardnumber').text('');
          // $('#sex').text('');
        } else if ( type == 'job' ) {
          $('#cardnumber').text(gradelabel);
          $('#cardnumber').css('margin-top', '20px');
          $('#dob').text('');
          $('#signature').text('');
          $('#name').css('margin-top', '63px');
          $('img').css('margin-left', '40px')
			  $('#id-card').css('background', 'url(assets/images/'+jobname2+'.png)');
        } else {
          $('#id-card').css('background', 'url(assets/images/unemployed.png)');
        }
      } else if ( type == 'weapon' ) {
        // $('img').hide();
        $('img').attr('src', mugshot);
        // $('#name').css('margin-top', '35px');
        // $('#name').css('color', 'red');
        // $('#name').text(userData.firstname + ' ' + userData.lastname);
        $('#name').text('');
        $('#dob').text('');
        $('#height').text('');
        $('#signature').text('');
        $('#cardnumber').text('');
        // $('#signature').text(userData.jobname/* userData.firstname + ' ' + userData.lastname */);

        if ( jobname == 'admin' ) {
          $('#name').text('غير مسجل');
        } else {
          // $('#name').text(userData.firstname + ' ' + userData.lastname);
        }

        $('#id-card').css('background', 'url(assets/images/firearm.png)');
      }

    else if ( type == 'market' ) {
        $('img').show();
        // $('#name').css('color', '#d9d9d9');
        // $('#name').text(userData.firstname + ' ' + userData.lastname);
        $('#name').text('');
        $('#dob').text(userData.dateofbirth);
        $('#height').text(userData.height);		
        $('#signature').text(userData.jobname/* userData.firstname + ' ' + userData.lastname */);

        $('#id-card').css('background', 'url(assets/images/market.png)');
      }
      
      else if ( type == 'gold' ) {
        //$('img').show();
        $('img').hide();
        
        $('#id-card').css('background', 'url(assets/images/gold.png)');
      }

      else if ( type == 'bronze' ) {
        $('img').show();
        $('#id-card').css('background', 'url(assets/images/bronze.png)');
      }
      else if ( type == 'sponser' ) {
        console.log(event.data.array.rolename)
        //$('img').show();
        $('img').hide();

        $('#id-card').css('background', 'url(assets/images/'+event.data.array.rolename+'.png)');
      }
      $('#id-card').show();
    } 
    else if (event.data.action == 'close') {
      $('#name').text('');
      $('#dob').text('');
      $('#height').text('');
      $('#signature').text('');
      $('#cardnumber').text('');
      // $('#sex').text('');
      $('#id-card').hide();
      $('#licenses').html('');
      $('#dob').css('margin-right', '90px');
    }
  });
});
