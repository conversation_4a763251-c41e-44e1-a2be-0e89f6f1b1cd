-- Test script to verify restart fixes
-- This script can be used to test the resource restart functionality

local testsPassed = 0
local totalTests = 0

-- Function to run a test
local function runTest(testName, testFunction)
    totalTests = totalTests + 1
    print('[TEST] Running: ' .. testName)
    
    local success, result = pcall(testFunction)
    
    if success and result then
        testsPassed = testsPassed + 1
        print('[TEST] ✓ PASSED: ' .. testName)
    else
        print('[TEST] ✗ FAILED: ' .. testName .. ' - ' .. tostring(result))
    end
end

-- Test 1: Check if Config is loaded
local function testConfigLoaded()
    return Config ~= nil and Config.Main ~= nil
end

-- Test 2: Check if ParkVehicles function exists
local function testParkVehiclesExists()
    return ParkVehicles ~= nil and type(ParkVehicles) == 'function'
end

-- Test 3: Check if streaming assets are initialized
local function testStreamingInitialized()
    local fontId = exports['esx_misc3']:GetCustomFont('A9eelsh')
    return fontId ~= nil
end

-- Test 4: Check if MySQL is ready
local function testMySQLReady()
    return MySQL ~= nil and MySQL.ready ~= nil
end

-- Test 5: Check if ESX is loaded
local function testESXLoaded()
    return ESX ~= nil
end

-- Run all tests
Citizen.CreateThread(function()
    Citizen.Wait(5000) -- Wait for everything to load
    
    print('[TEST] Starting esx_misc3 restart tests...')
    print('[TEST] ==========================================')
    
    runTest('Config Loaded', testConfigLoaded)
    runTest('ParkVehicles Function Exists', testParkVehiclesExists)
    runTest('Streaming Assets Initialized', testStreamingInitialized)
    runTest('MySQL Ready', testMySQLReady)
    runTest('ESX Loaded', testESXLoaded)
    
    print('[TEST] ==========================================')
    print('[TEST] Results: ' .. testsPassed .. '/' .. totalTests .. ' tests passed')
    
    if testsPassed == totalTests then
        print('[TEST] ✓ All tests passed! Resource restart should work correctly.')
    else
        print('[TEST] ✗ Some tests failed. Check the issues above.')
    end
end)

-- Command to manually run tests
RegisterCommand('testmiscrestart', function()
    print('[TEST] Manual test triggered...')
    -- Restart the resource to test
    ExecuteCommand('restart esx_misc3')
end, false)

-- Export test function for external use
exports('RunRestartTests', function()
    return {
        passed = testsPassed,
        total = totalTests,
        success = testsPassed == totalTests
    }
end)
