--get shared config
RegisterNetEvent('esx_misc3:spawned')
AddEventHandler('esx_misc3:spawned', function()
	TriggerClientEvent('esx_misc3:updateconfig', source, Config)
end)

AddEventHandler('onResourceStart', function(resource)
	if resource == GetCurrentResourceName() then
		print('[esx_misc3] Server resource started, initializing...')
		Citizen.Wait(5000)
		TriggerClientEvent('esx_misc3:updateconfig', -1, Config)
		print('[esx_misc3] Configuration sent to all clients')
	end
end)

AddEventHandler('onResourceStop', function(resource)
	if resource == GetCurrentResourceName() then
		print('[esx_misc3] Server resource stopped, cleaning up...')
		-- Clean up any server-side resources if needed
	end
end)

exports('GetDrugsConfig', function()
	return Config.ZonesDrugs
end)