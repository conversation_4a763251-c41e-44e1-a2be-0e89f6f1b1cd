-- Streaming Fix for esx_misc3
-- This file handles streaming issues that occur during resource restart

local streamingInitialized = false
local fontCache = {}

-- Function to safely initialize streaming assets
local function InitializeStreaming()
    if not streamingInitialized then
        Citizen.CreateThread(function()
            -- Wait for game to be fully loaded
            while not HasStreamedTextureDictLoaded("commonmenu") do
                Citizen.Wait(100)
            end
            
            -- Initialize custom fonts safely
            if not fontCache['A9eelsh'] then
                fontCache['A9eelsh'] = RegisterFontId('A9eelsh')
                print('[esx_misc3] Custom font A9eelsh registered successfully')
            end
            
            streamingInitialized = true
            print('[esx_misc3] Streaming assets initialized')
        end)
    end
end

-- Function to clean up streaming assets
local function CleanupStreaming()
    streamingInitialized = false
    fontCache = {}
    print('[esx_misc3] Streaming assets cleaned up')
end

-- Handle resource events
AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        Citizen.Wait(2000) -- Give time for other resources to load
        InitializeStreaming()
    end
end)

AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        CleanupStreaming()
    end
end)

-- Export function to get registered font
exports('GetCustomFont', function(fontName)
    return fontCache[fontName] or 0
end)

-- Initialize on script load
InitializeStreaming()
